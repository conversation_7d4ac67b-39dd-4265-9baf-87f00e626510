// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package main

type Account struct {
	ID     string   `json:"id"`
	Name   string   `json:"name"`
	Orders []*Order `json:"orders"`
}

type AccountInput struct {
	Name string `json:"name"`
}

type Mutation struct {
}

type Order struct {
	ID         string       `json:"id"`
	CreateAt   string       `json:"createAt"`
	TotalPrice float64      `json:"totalPrice"`
	Items      []*OrderItem `json:"items"`
}

type OrderInput struct {
	AccountID string            `json:"accountId"`
	Items     []*OrderItemInput `json:"items"`
}

type OrderItem struct {
	ID       string   `json:"id"`
	Quantity int      `json:"quantity"`
	Product  *Product `json:"product"`
	Price    float64  `json:"price"`
}

type OrderItemInput struct {
	ProductID string `json:"productId"`
	Quantity  int    `json:"quantity"`
}

type PaginationInput struct {
	Skip int `json:"skip"`
	Take int `json:"take"`
}

type Product struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Price       float64 `json:"price"`
	Description string  `json:"description"`
}

type ProductInput struct {
	Name        string  `json:"name"`
	Price       float64 `json:"price"`
	Description string  `json:"description"`
}

type Query struct {
}
